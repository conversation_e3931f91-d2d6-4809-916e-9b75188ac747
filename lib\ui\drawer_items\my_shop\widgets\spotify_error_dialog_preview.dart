import 'package:flutter/material.dart';
import 'spotify_error_dialog.dart';

/// Preview page to test the Spotify-style error dialog
class SpotifyErrorDialogPreview extends StatelessWidget {
  const SpotifyErrorDialogPreview({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF121212), // Spotify dark background
      appBar: AppBar(
        title: const Text('Error Dialog Preview'),
        backgroundColor: const Color(0xFF1A1A1A),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () {
                SpotifyErrorDialog.show(
                  context,
                  title: 'Deletion Failed',
                  message: 'Failed to delete product. Please try again.',
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1DB954),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
              child: const Text(
                'Show Error Dialog',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Tap the button to preview the error dialog',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
