import 'package:equatable/equatable.dart';
import 'package:business_app/models/product/product.dart';
import 'product_event.dart';

enum ProductStatus {
  initial,
  loading,
  loaded,
  error,
  refreshing,
  loadingMore,
  adding,
  updating,
  deleting,
  success,
}

class ProductState extends Equatable {
  final ProductStatus status;
  final List<Product> products;
  final List<Product> filteredProducts;
  final String? selectedCategory;
  final String searchQuery;
  final ProductSortType sortType;
  final bool hasReachedMax;
  final String? errorMessage;
  final String? successMessage;
  final String? deletionErrorMessage;
  final Product? selectedProduct;
  final Map<String, int> categoryCount;
  final bool isSelectionMode;
  final Set<String> selectedProductIds;

  const ProductState({
    this.status = ProductStatus.initial,
    this.products = const [],
    this.filteredProducts = const [],
    this.selectedCategory,
    this.searchQuery = '',
    this.sortType = ProductSortType.dateDesc,
    this.hasReachedMax = false,
    this.errorMessage,
    this.successMessage,
    this.deletionErrorMessage,
    this.selectedProduct,
    this.categoryCount = const {},
    this.isSelectionMode = false,
    this.selectedProductIds = const {},
  });

  ProductState copyWith({
    ProductStatus? status,
    List<Product>? products,
    List<Product>? filteredProducts,
    String? selectedCategory,
    String? searchQuery,
    ProductSortType? sortType,
    bool? hasReachedMax,
    String? errorMessage,
    String? successMessage,
    String? deletionErrorMessage,
    Product? selectedProduct,
    Map<String, int>? categoryCount,
    bool? isSelectionMode,
    Set<String>? selectedProductIds,
    bool clearSelectedCategory = false,
    bool clearErrorMessage = false,
    bool clearSuccessMessage = false,
    bool clearDeletionErrorMessage = false,
  }) {
    return ProductState(
      status: status ?? this.status,
      products: products ?? this.products,
      filteredProducts: filteredProducts ?? this.filteredProducts,
      selectedCategory:
          clearSelectedCategory
              ? null
              : (selectedCategory ?? this.selectedCategory),
      searchQuery: searchQuery ?? this.searchQuery,
      sortType: sortType ?? this.sortType,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      errorMessage:
          clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
      successMessage:
          clearSuccessMessage ? null : (successMessage ?? this.successMessage),
      deletionErrorMessage:
          clearDeletionErrorMessage
              ? null
              : (deletionErrorMessage ?? this.deletionErrorMessage),
      selectedProduct: selectedProduct ?? this.selectedProduct,
      categoryCount: categoryCount ?? this.categoryCount,
      isSelectionMode: isSelectionMode ?? this.isSelectionMode,
      selectedProductIds: selectedProductIds ?? this.selectedProductIds,
    );
  }

  // Helper methods
  List<Product> get displayProducts {
    List<Product> products = this.products;

    // Apply category filter
    if (selectedCategory != null && selectedCategory!.isNotEmpty) {
      products = products.where((p) => p.category == selectedCategory).toList();
    }

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      products =
          products.where((p) {
            return p.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
                (p.description?.toLowerCase().contains(
                      searchQuery.toLowerCase(),
                    ) ??
                    false) ||
                p.category.toLowerCase().contains(searchQuery.toLowerCase()) ||
                (p.brand?.toLowerCase().contains(searchQuery.toLowerCase()) ??
                    false) ||
                p.tags.any(
                  (tag) =>
                      tag.toLowerCase().contains(searchQuery.toLowerCase()),
                );
          }).toList();
    }

    // Apply sorting
    switch (sortType) {
      case ProductSortType.nameAsc:
        products.sort((a, b) => a.name.compareTo(b.name));
        break;
      case ProductSortType.nameDesc:
        products.sort((a, b) => b.name.compareTo(a.name));
        break;
      case ProductSortType.priceAsc:
        products.sort((a, b) => a.finalPrice.compareTo(b.finalPrice));
        break;
      case ProductSortType.priceDesc:
        products.sort((a, b) => b.finalPrice.compareTo(a.finalPrice));
        break;
      case ProductSortType.dateAsc:
        products.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case ProductSortType.dateDesc:
        products.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case ProductSortType.stockAsc:
        products.sort((a, b) => a.stockQuantity.compareTo(b.stockQuantity));
        break;
      case ProductSortType.stockDesc:
        products.sort((a, b) => b.stockQuantity.compareTo(a.stockQuantity));
        break;
      case ProductSortType.ratingAsc:
        products.sort((a, b) => a.rating.compareTo(b.rating));
        break;
      case ProductSortType.ratingDesc:
        products.sort((a, b) => b.rating.compareTo(a.rating));
        break;
    }

    return products;
  }

  bool get hasProducts => products.isNotEmpty;

  bool get hasFilteredProducts => displayProducts.isNotEmpty;

  int get totalProducts => products.length;

  int get availableProducts => products.where((p) => p.isAvailable).length;

  int get outOfStockProducts => products.where((p) => !p.isInStock).length;

  int get lowStockProducts => products.where((p) => p.isLowStock).length;

  List<String> get categories {
    return products.map((p) => p.category).toSet().toList()..sort();
  }

  Map<String, int> get categoryCounts {
    final counts = <String, int>{};
    for (final product in products) {
      counts[product.category] = (counts[product.category] ?? 0) + 1;
    }
    return counts;
  }

  double get totalInventoryValue {
    return products.fold(
      0.0,
      (sum, product) => sum + (product.finalPrice * product.stockQuantity),
    );
  }

  double get averageProductPrice {
    if (products.isEmpty) return 0.0;
    return products.fold(0.0, (sum, product) => sum + product.finalPrice) /
        products.length;
  }

  @override
  List<Object?> get props => [
    status,
    products,
    filteredProducts,
    selectedCategory,
    searchQuery,
    sortType,
    hasReachedMax,
    errorMessage,
    successMessage,
    deletionErrorMessage,
    selectedProduct,
    categoryCount,
    isSelectionMode,
    selectedProductIds,
  ];

  @override
  String toString() {
    return 'ProductState(status: $status, products: ${products.length}, category: $selectedCategory, search: $searchQuery)';
  }
}
